#!/bin/bash

# 离线Ubuntu系统时间同步脚本
# 适用于没有网络连接且未安装ntpdate的机器狗系统

NTP_SERVER="10.21.41.235"

echo "=========================================="
echo "离线Ubuntu系统时间同步脚本"
echo "NTP服务器: $NTP_SERVER"
echo "=========================================="

# 显示当前时间
echo "当前系统时间:"
date
echo "当前硬件时钟:"
sudo hwclock --show

echo ""
echo "开始时间同步过程..."

# 1. 禁用现有时间同步服务
echo "1. 禁用现有时间同步服务..."
if systemctl list-units --type=service | grep -q chronyd; then
    echo "  停止 chronyd 服务"
    sudo systemctl stop chronyd 2>/dev/null
    sudo systemctl disable chronyd 2>/dev/null
elif systemctl list-units --type=service | grep -q systemd-timesyncd; then
    echo "  停止 systemd-timesyncd 服务"
    sudo systemctl stop systemd-timesyncd 2>/dev/null
    sudo systemctl disable systemd-timesyncd 2>/dev/null
else
    echo "  未找到运行中的时间同步服务"
fi

# 2. 禁用自动时间同步
echo "2. 禁用自动时间同步..."
sudo timedatectl set-ntp false 2>/dev/null

# 3. 检查可用的时间同步工具
echo "3. 检查可用的时间同步工具..."

if command -v ntpdate &> /dev/null; then
    echo "  找到 ntpdate，使用 ntpdate 进行同步"
    SYNC_METHOD="ntpdate"
elif command -v sntp &> /dev/null; then
    echo "  找到 sntp，使用 sntp 进行同步"
    SYNC_METHOD="sntp"
elif systemctl list-units --type=service | grep -q systemd-timesyncd; then
    echo "  使用 systemd-timesyncd 进行同步"
    SYNC_METHOD="systemd-timesyncd"
else
    echo "  警告: 未找到标准NTP客户端工具"
    echo "  将尝试使用系统调用进行时间同步"
    SYNC_METHOD="manual"
fi

# 4. 执行时间同步
echo "4. 执行时间同步..."

case $SYNC_METHOD in
    "ntpdate")
        echo "  使用 ntpdate 同步时间..."
        if sudo ntpdate -s $NTP_SERVER; then
            echo "  ✓ ntpdate 同步成功"
            SYNC_SUCCESS=true
        else
            echo "  ✗ ntpdate 同步失败"
            SYNC_SUCCESS=false
        fi
        ;;
    
    "sntp")
        echo "  使用 sntp 同步时间..."
        if sudo sntp -s $NTP_SERVER; then
            echo "  ✓ sntp 同步成功"
            SYNC_SUCCESS=true
        else
            echo "  ✗ sntp 同步失败"
            SYNC_SUCCESS=false
        fi
        ;;
    
    "systemd-timesyncd")
        echo "  配置 systemd-timesyncd..."
        # 临时配置NTP服务器
        echo "NTP=$NTP_SERVER" | sudo tee /etc/systemd/timesyncd.conf.d/local.conf > /dev/null
        sudo systemctl restart systemd-timesyncd
        sudo timedatectl set-ntp true
        sleep 10  # 等待同步
        if timedatectl status | grep -q "synchronized: yes"; then
            echo "  ✓ systemd-timesyncd 同步成功"
            SYNC_SUCCESS=true
        else
            echo "  ✗ systemd-timesyncd 同步失败"
            SYNC_SUCCESS=false
        fi
        ;;
    
    "manual")
        echo "  尝试手动网络时间获取..."
        echo "  警告: 这种方法可能不够精确"
        # 这里可以添加其他手动同步方法
        SYNC_SUCCESS=false
        ;;
esac

# 5. 更新硬件时钟
if [ "$SYNC_SUCCESS" = true ]; then
    echo "5. 更新硬件时钟..."
    sudo hwclock -w
    if [ $? -eq 0 ]; then
        echo "  ✓ 硬件时钟更新成功"
    else
        echo "  ✗ 硬件时钟更新失败"
    fi
else
    echo "5. 跳过硬件时钟更新（时间同步失败）"
fi

# 6. 重新启用自动时间同步（可选）
echo "6. 重新启用自动时间同步..."
sudo timedatectl set-ntp true 2>/dev/null

# 7. 显示最终结果
echo ""
echo "=========================================="
echo "时间同步完成"
echo "=========================================="
echo "最终系统时间:"
date
echo "最终硬件时钟:"
sudo hwclock --show

if [ "$SYNC_SUCCESS" = true ]; then
    echo ""
    echo "✓ 时间同步成功完成！"
else
    echo ""
    echo "✗ 时间同步失败，请检查："
    echo "  1. NTP服务器 $NTP_SERVER 是否可达"
    echo "  2. 网络连接是否正常"
    echo "  3. 防火墙设置是否正确"
    echo ""
    echo "您可以尝试手动设置时间："
    echo "  sudo date -s 'YYYY-MM-DD HH:MM:SS'"
    echo "  sudo hwclock -w"
fi

echo "=========================================="
