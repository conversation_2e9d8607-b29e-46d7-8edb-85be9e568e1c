#!/bin/bash

config_file="/var/opt/robot/conf/HWI.toml"
backup_file="${config_file}.bak.$(date +%Y%m%d_%H%M%S)"
debs_files=("CA9B_103_2509022030.debs" "CA9B_104_2509021700.debs" "CA9B_106_2509021700.debs")
other_files=("release_note.json")
target_dir="/var/opt/robot"


if [ ! -f "$config_file" ]; then
    echo "错误: 配置文件 $config_file 不存在!"
    exit 1
fi


if [ "$EUID" -ne 0 ]; then
    echo "请使用sudo运行此脚本"
    exit 1
fi


echo "正在创建备份: $backup_file"
cp "$config_file" "$backup_file" || {
    echo "错误: 创建备份失败!"
    exit 1
}

chmod +w "$config_file" || {
    echo "错误: 无法添加写入权限!"
    exit 1
}


echo "当前第5行内容:"
sed -n '5p' "$config_file"


echo "请选择要设置的设备型号:"
echo "1) 山猫M20 (修改为 STD)"
echo "2) 山猫M20 Pro (修改为 PRO)"
read -p "请输入选择 (1 或 2): " choice

case $choice in
    1) new_value="STD" ;;
    2) new_value="PRO" ;;
    *)
        echo "错误: 无效的选择!"
        exit 1
        ;;
esac


sed -i "5s/type[[:space:]]*=[[:space:]]*\"[^\"]*\"/type = \"$new_value\"/" "$config_file"

if [ $? -eq 0 ]; then
    echo "修改成功!"
    echo "修改后第5行内容:"
    sed -n '5p' "$config_file"
else
    echo "错误: 修改失败!"
    echo "正在从备份恢复原文件..."
    cp "$backup_file" "$config_file"
    exit 1
fi

echo "操作完成。原文件已备份至: $backup_file"


for file in "${debs_files[@]}" "${other_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "错误: 文件 $file 不存在!"
        exit 1
    fi
done


echo "正在复制版本包文件到 $target_dir..."
cp "${debs_files[@]}" "${other_files[@]}" "$target_dir/" || {
    echo "错误: 复制文件失败!"
    exit 1
}
echo "文件复制完成"


echo "正在执行安装脚本..."
cd "$target_dir" || {
    echo "错误: 无法进入 $target_dir 目录!"
    exit 1
}

echo "当前目录: $(pwd)"
echo "检查安装脚本文件..."
ls -la CA9B_103_2509022030.debs

if [ ! -f "CA9B_103_2509022030.debs" ]; then
    echo "错误: 安装脚本文件不存在!"
    echo "目录内容:"
    ls -la
    exit 1
fi

echo "执行安装脚本: sudo bash CA9B_103_2509022030.debs"
sudo bash CA9B_103_2509022030.debs || {
    echo "错误: 安装脚本执行失败!"
    exit 1
}
echo "安装脚本执行完成"

# 清理：删除脚本文件
echo "正在清理脚本文件..."
SCRIPT_PATH="$0"
if [ -f "$SCRIPT_PATH" ]; then
    rm -f "$SCRIPT_PATH"
    echo "脚本文件已删除: $SCRIPT_PATH"
else
    echo "脚本文件路径: $SCRIPT_PATH"
fi


rm -rf /home/<USER>/m20test2.sh

























