#!/bin/bash
config_file="/var/opt/robot/conf/HWI.toml"
backup_file="${config_file}.bak.$(date +%Y%m%d_%H%M%S)"

if [ ! -f "$config_file" ]; then
    echo "错误: 配置文件 $config_file 不存在!"
    exit 1
fi


echo "正在创建备份: $backup_file"
sudo cp "$config_file" "$backup_file" || {
    echo "错误: 创建备份失败!"
    exit 1
}


sudo chmod +w "$config_file" || {
    echo "错误: 无法添加写入权限!"
    exit 1
}




echo "当前第5行内容:"
sudo sed -n '5p' "$config_file"

echo "请选择要设置的设备型号:"
echo "1) 山猫M20 (修改为 STD)"
echo "2) 山猫M20 Pro (修改为 PRO)"
read -p "请输入选择 (1 或 2): " choice





case $choice in
    1)
        new_value="STD"
        ;;
    2)
        new_value="PRO"
        ;;
    *)
        echo "错误: 无效的选择!"
        exit 1
        ;;
esac



sudo sed -i "5s/type[[:space:]]*=[[:space:]]*\"[^\"]*\"/type = \"$new_value\"/" "$config_file"


if [ $? -eq 0 ]; then
    echo "修改成功!"
    echo "修改后第5行内容:"
    sudo sed -n '5p' "$config_file"
else
    echo "错误: 修改失败!"
    echo "正在从备份恢复原文件..."
    sudo cp "$backup_file" "$config_file"
    exit 1
fi



echo "操作完成。原文件已备份至: $backup_file"



sudo cp CA9B_103_2508052100.debs CA9B_104_2508052100.debs CA9B_106_2508071500.debs release_note.json /var/opt/robot/




cd /var/opt/robot/ && sudo bash CA9B_103_2508052100.debs




cd /opt/robot/bin/ || {
    echo "错误: 无法进入 /opt/robot/bin/ 目录!"
    exit 1
}






echo "3.3 检查Pos值是否为0.000..."
echo "请手动检查Pos值是否为0.000，完成后按任意键继续..."



sh show_deb_version.sh













