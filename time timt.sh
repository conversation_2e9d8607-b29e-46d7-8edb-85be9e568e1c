#!/bin/bash

# 禁用时间同步服务
echo "禁用时间同步服务..."
if systemctl list-units --type=service | grep -q chronyd; then
    sudo systemctl stop chronyd
    sudo systemctl disable chronyd
elif systemctl list-units --type=service | grep -q systemd-timesyncd; then
    sudo systemctl stop systemd-timesyncd
    sudo systemctl disable systemd-timesyncd
else
    echo "未找到已知的时间同步服务。"
fi

# 禁用自动时间同步
echo "禁用自动时间同步..."
sudo timedatectl set-ntp false

# 检查可用的时间同步工具
echo "检查可用的时间同步工具..."
if command -v ntpdate &> /dev/null; then
    echo "使用 ntpdate 进行时间同步"
    SYNC_TOOL="ntpdate"
elif command -v sntp &> /dev/null; then
    echo "使用 sntp 进行时间同步"
    SYNC_TOOL="sntp"
elif command -v chronyd &> /dev/null; then
    echo "使用 chrony 进行时间同步"
    SYNC_TOOL="chrony"
else
    echo "警告: 未找到可用的NTP客户端工具"
    echo "将尝试使用 timedatectl 进行网络时间同步"
    SYNC_TOOL="timedatectl"
fi

# 使用可用工具进行时间同步
echo "使用 $SYNC_TOOL 同步系统时间..."
NTP_SERVER="10.21.41.235"

case $SYNC_TOOL in
    "ntpdate")
        if sudo ntpdate $NTP_SERVER; then
            echo "ntpdate 时间同步成功"
            sync_success=true
        else
            echo "ntpdate 同步失败"
            sync_success=false
        fi
        ;;
    "sntp")
        if sudo sntp -s $NTP_SERVER; then
            echo "sntp 时间同步成功"
            sync_success=true
        else
            echo "sntp 同步失败"
            sync_success=false
        fi
        ;;
    "chrony")
        if sudo chrony sources -v && sudo chrony makestep; then
            echo "chrony 时间同步成功"
            sync_success=true
        else
            echo "chrony 同步失败"
            sync_success=false
        fi
        ;;
    "timedatectl")
        echo "尝试使用 timedatectl 进行时间同步..."
        if sudo timedatectl set-ntp true; then
            sleep 5  # 等待同步
            echo "timedatectl 时间同步尝试完成"
            sync_success=true
        else
            echo "timedatectl 同步失败"
            sync_success=false
        fi
        ;;
    *)
        echo "错误: 没有可用的时间同步工具"
        echo "请手动安装 ntpdate 或其他NTP客户端"
        exit 1
        ;;
esac

if [ "$sync_success" = true ]; then
    echo "时间同步成功，正在更新硬件时钟..."
    sudo hwclock -w
    echo "硬件时钟已更新"
else
    echo "错误: 时间同步失败，请检查："
    echo "1. 网络连接是否正常"
    echo "2. NTP服务器 $NTP_SERVER 是否可达"
    echo "3. 防火墙是否阻止NTP端口(123)"
    exit 1
fi

echo "重新启用自动时间同步..."
sudo timedatectl set-ntp true

echo "验证系统时间和硬件时钟..."
echo "系统时间："
date
echo "硬件时钟："
sudo hwclock --show

rm -rf /home/<USER>/time.sh



