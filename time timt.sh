#!/bin/bash

# 禁用时间同步服务
echo "禁用时间同步服务..."
if systemctl list-units --type=service | grep -q chronyd; then
    sudo systemctl stop chronyd
    sudo systemctl disable chronyd
elif systemctl list-units --type=service | grep -q systemd-timesyncd; then
    sudo systemctl stop systemd-timesyncd
    sudo systemctl disable systemd-timesyncd
else
    echo "未找到已知的时间同步服务。"
fi

# 禁用自动时间同步
echo "禁用自动时间同步..."
sudo timedatectl set-ntp false

# 安装NTP客户端（如果尚未安装）
if ! command -v ntpdate &> /dev/null; then
    echo "检测到系统未安装ntpdate，正在安装..."
    sudo apt-get update
    sudo apt-get install -y ntpdate
    if [ $? -eq 0 ]; then
        echo "ntpdate 安装成功"
    else
        echo "错误: ntpdate 安装失败"
        exit 1
    fi
else
    echo "ntpdate 已安装"
fi

# 使用NTP同步系统时间
echo "使用NTP同步系统时间..."
if sudo ntpdate 10.21.41.235; then
    echo "NTP时间同步成功"
    sudo hwclock -w
    echo "硬件时钟已更新"
else
    echo "错误: NTP时间同步失败，请检查："
    echo "1. 网络连接是否正常"
    echo "2. NTP服务器 10.21.41.235 是否可达"
    echo "3. 防火墙是否阻止NTP端口(123)"
    exit 1
fi

echo "重新启用自动时间同步..."
sudo timedatectl set-ntp true

echo "验证系统时间和硬件时钟..."
echo "系统时间："
date
echo "硬件时钟："
sudo hwclock --show

rm -rf /home/<USER>/time.sh



