@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: Minicom命令执行脚本
:: 提供清晰的minicom操作指导

echo ==========================================
echo           MINICOM 操作指南
echo ==========================================
echo.

:: 颜色设置
set "ps_cyan=Write-Host -ForegroundColor Cyan"
set "ps_yellow=Write-Host -ForegroundColor Yellow"
set "ps_green=Write-Host -ForegroundColor Green"
set "ps_red=Write-Host -ForegroundColor Red"

echo 📋 操作步骤：
echo.

echo 🔸 步骤1: 启动minicom
powershell -Command "%ps_cyan% '   sudo minicom'"
echo.

echo 🔸 步骤2: 在minicom中依次执行以下命令：
echo.
powershell -Command "%ps_yellow% '   ➤ sh'"
echo     (进入shell模式)
echo.
powershell -Command "%ps_yellow% '   ➤ show all version'"
echo     (显示所有版本信息)
echo.
powershell -Command "%ps_yellow% '   ➤ show all error'"
echo     (显示所有错误信息)
echo.
powershell -Command "%ps_yellow% '   ➤ show battery'"
echo     (显示电池状态)
echo.

echo 🔸 步骤3: 退出minicom
powershell -Command "%ps_cyan% '   Ctrl+A, 然后按 X'"
echo     (或者按 Ctrl+A, Q 退出)
echo.

echo ==========================================
echo.

:: 检查环境并提供启动选项
echo 🚀 启动选项：
echo.

:: 检查WSL
wsl --list >nul 2>&1
if %errorlevel% equ 0 (
    powershell -Command "%ps_green% '✓ WSL环境可用'"
    echo   选项1: 在WSL中启动minicom
    echo.
)

:: 检查PuTTY
where putty >nul 2>&1
if %errorlevel% equ 0 (
    powershell -Command "%ps_green% '✓ PuTTY可用'"
    echo   选项2: 使用PuTTY串口连接
    echo.
)

:: 检查TeraTerm
where teraterm >nul 2>&1
if %errorlevel% equ 0 (
    powershell -Command "%ps_green% '✓ TeraTerm可用'"
    echo   选项3: 使用TeraTerm串口连接
    echo.
)

echo ==========================================
echo.

:menu
echo 请选择启动方式：
echo.
echo [1] WSL + minicom (推荐)
echo [2] PuTTY 串口连接
echo [3] TeraTerm 串口连接
echo [4] 显示命令清单 (复制粘贴用)
echo [5] 退出
echo.
set /p "choice=请输入选择 (1-5): "

if "!choice!"=="1" goto :wsl_minicom
if "!choice!"=="2" goto :putty_serial
if "!choice!"=="3" goto :teraterm_serial
if "!choice!"=="4" goto :show_commands
if "!choice!"=="5" goto :exit
echo 无效选择，请重新输入
goto :menu

:wsl_minicom
echo.
powershell -Command "%ps_green% '启动WSL minicom...'"
echo.
echo 即将执行: wsl sudo minicom
echo 请在minicom中按顺序执行上述命令
echo.
pause
wsl sudo minicom
echo.
powershell -Command "%ps_green% 'minicom会话结束'"
goto :menu

:putty_serial
echo.
powershell -Command "%ps_green% '启动PuTTY...'"
echo.
echo PuTTY配置提示：
echo 1. 连接类型选择: Serial
echo 2. 设置正确的COM端口 (如COM1, COM3等)
echo 3. 波特率通常设置为: 115200
echo 4. 数据位: 8, 停止位: 1, 校验: None
echo.
pause
start putty
goto :menu

:teraterm_serial
echo.
powershell -Command "%ps_green% '启动TeraTerm...'"
echo.
echo TeraTerm配置提示：
echo 1. 选择Serial端口连接
echo 2. 配置正确的串口参数
echo 3. 连接后执行上述命令序列
echo.
pause
start teraterm
goto :menu

:show_commands
echo.
echo ==========================================
echo         复制粘贴用命令清单
echo ==========================================
echo.
echo sudo minicom
echo.
echo # 在minicom中执行：
echo sh
echo show all version
echo show all error
echo show battery
echo.
echo # 退出minicom: Ctrl+A, X
echo.
echo ==========================================
echo.
pause
goto :menu

:exit
echo.
powershell -Command "%ps_green% '脚本结束，感谢使用！'"
pause
exit /b 0
