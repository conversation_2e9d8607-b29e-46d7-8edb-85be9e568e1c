@echo off
echo ========================================
echo 分步检验SSH远程执行
echo ========================================

echo.
echo 第1步：测试SSH连接...
ssh user@************ "echo 'SSH连接测试成功'"
if %errorlevel% neq 0 (
    echo 错误：SSH连接失败！
    pause
    exit /b 1
)

echo.
echo 第2步：检查project.zip文件是否存在...
ssh user@************ "ls -la ~/project.zip"
if %errorlevel% neq 0 (
    echo 错误：project.zip文件不存在！
    echo 请先执行文件传输：
    echo scp "C:\Users\<USER>\Desktop\m20脚本\project.zip" user@************:~/project.zip
    pause
    exit /b 1
)

echo.
echo 第3步：解压project.zip文件...
ssh user@************ "unzip -o ~/project.zip -d ~/"
if %errorlevel% neq 0 (
    echo 错误：文件解压失败！
    pause
    exit /b 1
)

echo.
echo 第4步：检查解压结果...
ssh user@************ "ls -la ~/project/"
if %errorlevel% neq 0 (
    echo 错误：project目录不存在或为空！
    pause
    exit /b 1
)

echo.
echo 第5步：检查m20test2.sh脚本文件...
ssh user@************ "ls -la ~/project/m20test2.sh"
if %errorlevel% neq 0 (
    echo 错误：m20test2.sh脚本文件不存在！
    pause
    exit /b 1
)

echo.
echo 第6步：设置脚本执行权限...
ssh user@************ "chmod +x ~/project/m20test2.sh"
if %errorlevel% neq 0 (
    echo 错误：设置执行权限失败！
    pause
    exit /b 1
)

echo.
echo 第7步：验证权限设置...
ssh user@************ "ls -la ~/project/m20test2.sh"

echo.
echo 第8步：测试sudo权限...
ssh -t user@************ "sudo echo 'sudo权限测试成功'"
if %errorlevel% neq 0 (
    echo 错误：sudo权限测试失败！
    pause
    exit /b 1
)

echo.
echo 第9步：执行脚本...
echo 注意：这一步需要您输入sudo密码
pause
ssh -t user@************ "sudo bash ~/project/m20test2.sh"

echo.
echo ========================================
echo 所有步骤执行完成！
echo ========================================
pause
