{"PRODUCT_NAME": "M20", "DEVICE_TYPE": "PRO", "VERSION": "1.1.4", "DEBS": [{"host": "AOS", "deb": "CA9B_103_2509022030.debs", "version": "452486719431238465dd6484529b1246", "md5": "452486719431238465dd6484529b1246", "autoInstall": true}, {"host": "NOS", "deb": "CA9B_106_2509021700.debs", "version": "b2c1a1f2882b6d2d9157d7027f8a946a", "md5": "b2c1a1f2882b6d2d9157d7027f8a946a"}, {"host": "GOS", "deb": "CA9B_104_2509021700.debs", "version": "d01f0f1165d6ebd1c9860cc42a86b76d", "md5": "d01f0f1165d6ebd1c9860cc42a86b76d"}], "APP": {"deb": "CA9B_V0.2.1(7)_arm64-v8a_6c28f99a_08251437.apk", "version": "0.2.1(7)", "forceUpgrade": true}, "README": {"zh": "更新常规模式下的基础步态；\\\n支持以自主导航模式在平地场景中低速执行巡检任务；\\\n支持以直线导航模式执行上下楼梯的导航巡检任务；\\\n新增辅助模式下支持上楼梯；\\\n优化基础平地侧移时机身不稳定问题；\\\n减少站立时的姿态调整频率（检测到升温趋势后调整）；\\\n优化导航过程中持续原地踏步问题；\\\n优化自主充电进退桩表现和成功率；\\\n提高定位丢失异常判断准确率。", "en": "Update the basic gait in the Regular mode;\\\nSupport low-speed inspection tasks in the Auto navigation mode on flat ground scenes;\\\nSupport the inspection tasks of going up and down stairs in the Straight navigation mode;\\\nAdd support for going up stairs in the Assist mode;\\\nOptimize the lateral translation of Basic gait on flat ground;\\\nReduce the frequency of posture adjustment when standing (adjust after the temperature rises);\\\nOptimize the problem of continuous in-place stepping during navigation;\\\nOptimize the performance of going in and out of the charging dock and success rate of autonomous charging;\\\nImprove the accuracy of abnormal judgment for lost positioning."}, "DEB_UPGRADE_STATUS": 0, "FIRMWARE_UPGRADE_STATUS": 0}