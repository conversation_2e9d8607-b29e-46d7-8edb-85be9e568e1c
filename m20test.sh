#!/bin/bash
# 山猫M20 Pro 三主机时间同步自动化脚本
# 支持103、104、106三个主机的时间同步操作

# ======================== 配置参数 ========================
WIFI_NAME="CA9B_NO.001_5G"
TARGET_TIME="2025-08-20 11:11:15"
DEB_FILES=("CA9B_103_2508052100.debs" "CA9B_104_2508052100.debs" "CA9B_106_2508071500.debs" "release_note")
DEB_SOURCE_DIR="/user/name/"
DEB_TARGET_DIR="/var/opt/robot/"
BATCH_NO="202508"
ROBOT_NO="001"
SERIAL_NO="CM20110${ROBOT_NO}"

# 主机配置
HOST_103="************"
HOST_104="************"
HOST_106="************"
SSH_USER="user"

# NTP服务器配置
NTP_SERVER="*************"

# ======================== 工具函数 ========================
log() { echo "[$(date +'%Y-%m-%d %H:%M:%S')] [$1] $2"; }
error_exit() { log "ERROR" "$1"; exit 1; }
check_result() { if [ $? -ne 0 ]; then error_exit "$1"; fi; }

# 检查SSH连接
check_ssh_connection() {
    local host=$1
    log "INFO" "检查到 ${host} 的SSH连接..."
    ssh -o ConnectTimeout=5 -o BatchMode=yes ${SSH_USER}@${host} "echo 'SSH连接正常'" > /dev/null 2>&1
    if [ $? -ne 0 ]; then
        log "WARN" "无法连接到 ${host}，请检查SSH配置或网络连接"
        return 1
    fi
    log "INFO" "${host} SSH连接正常"
    return 0
}

# ======================== 3. 运动版本包部署与安装 ========================
log "INFO" "开始部署运动版本包..."
# 检查版本包是否存在
for deb in "${DEB_FILES[@]}"; do
    if [ ! -f "${DEB_SOURCE_DIR}/${deb}" ]; then
        error_exit "版本包${DEB_SOURCE_DIR}/${deb}不存在，请检查源目录"
    fi
done
# 复制版本包到目标目录
sudo cp "${DEB_SOURCE_DIR}"/* "${DEB_TARGET_DIR}/"
check_result "复制版本包到${DEB_TARGET_DIR}失败"

# 安装103运动程序（104/106自动同步）
cd "${DEB_TARGET_DIR}" || error_exit "进入版本包目标目录失败"
sudo bash CA9B_103_STD_2508161300.debs > install_log_$(date +'%Y%m%d%H%M').log 2>&1
check_result "安装103运动程序失败，查看日志：${DEB_TARGET_DIR}/install_log_$(date +'%Y%m%d%H%M').log"

log "INFO" "运动版本包安装完成"

# ======================== 4. 权限配置与参数修改（STD→PRO + 序列号） ========================
log "INFO" "开始配置权限与版本参数..."
cd /var/opt/robot/conf/ || error_exit "进入配置目录失败"

# 赋予H/M配置文件权限
H_FILE=$(ls H*)  # 获取H开头的配置文件（如HWI.conf）
M_FILE=$(ls M*)  # 获取M开头的配置文件（如Machine.conf）
sudo chmod 777 "${H_FILE}" "${M_FILE}"
check_result "赋予配置文件权限失败"

# 修改版本为PRO版（替换STD为PRO）
sudo sed -i 's/STD/PRO/g' "${H_FILE}"
check_result "修改版本为PRO版失败"

# 修改批次号与序列号
sudo sed -i "s/^batch_no=.*/batch_no=\"${BATCH_NO}\"/g" "${M_FILE}"
sudo sed -i "s/^serial_no=.*/serial_no=\"${SERIAL_NO}\"/g" "${M_FILE}"
check_result "修改批次号（${BATCH_NO}）与序列号（${SERIAL_NO}）失败"

log "INFO" "权限与参数配置完成"

# ======================== 时间同步功能函数 ========================
# 禁用时间同步服务
disable_time_sync() {
    local host=$1
    log "INFO" "在 ${host} 上禁用时间同步服务..."

    if [ "$host" = "localhost" ]; then
        # 本地主机操作
        if systemctl list-units --type=service | grep -q chronyd; then
            sudo systemctl stop chronyd
            sudo systemctl disable chronyd
        elif systemctl list-units --type=service | grep -q systemd-timesyncd; then
            sudo systemctl stop systemd-timesyncd
            sudo systemctl disable systemd-timesyncd
        fi
        sudo timedatectl set-ntp false
    else
        # 远程主机操作
        ssh ${SSH_USER}@${host} "
            if systemctl list-units --type=service | grep -q chronyd; then
                sudo systemctl stop chronyd
                sudo systemctl disable chronyd
            elif systemctl list-units --type=service | grep -q systemd-timesyncd; then
                sudo systemctl stop systemd-timesyncd
                sudo systemctl disable systemd-timesyncd
            fi
            sudo timedatectl set-ntp false
        " > /dev/null 2>&1
    fi
}

# NTP时间同步功能
sync_time_with_ntp() {
    local host=$1

    log "INFO" "在 ${host} 上使用NTP同步时间..."

    if [ "$host" = "localhost" ] || [ "$host" = "************" ]; then
        # 本地主机NTP同步
        log "INFO" "禁用本地时间同步服务..."
        if systemctl list-units --type=service | grep -q chronyd; then
            sudo systemctl stop chronyd
            sudo systemctl disable chronyd
        elif systemctl list-units --type=service | grep -q systemd-timesyncd; then
            sudo systemctl stop systemd-timesyncd
            sudo systemctl disable systemd-timesyncd
        else
            log "INFO" "未找到已知的时间同步服务"
        fi

        log "INFO" "禁用自动时间同步..."
        sudo timedatectl set-ntp false

        log "INFO" "使用NTP同步系统时间..."
        sudo ntpdate ${NTP_SERVER}
        check_result "NTP时间同步失败"

        sudo hwclock -w
        check_result "写入硬件时钟失败"

        log "INFO" "重新启用自动时间同步..."
        sudo timedatectl set-ntp true

        # 验证时间设置
        log "INFO" "验证系统时间和硬件时钟..."
        log "INFO" "系统时间: $(date)"
        log "INFO" "硬件时钟: $(sudo hwclock --show)"

    else
        # 远程主机NTP同步
        if check_ssh_connection ${host}; then
            log "INFO" "在远程主机 ${host} 上执行NTP同步..."
            ssh ${SSH_USER}@${host} "
                # 禁用时间同步服务
                if systemctl list-units --type=service | grep -q chronyd; then
                    sudo systemctl stop chronyd
                    sudo systemctl disable chronyd
                elif systemctl list-units --type=service | grep -q systemd-timesyncd; then
                    sudo systemctl stop systemd-timesyncd
                    sudo systemctl disable systemd-timesyncd
                fi

                # 禁用自动时间同步
                sudo timedatectl set-ntp false

                # NTP同步
                sudo ntpdate ${NTP_SERVER}
                sudo hwclock -w

                # 重新启用自动时间同步
                sudo timedatectl set-ntp true

                echo '时间同步完成'
                echo '系统时间: '$(date)
                echo '硬件时钟: '$(sudo hwclock --show)
            " 2>/dev/null

            if [ $? -eq 0 ]; then
                log "INFO" "${host} NTP时间同步完成"
            else
                log "ERROR" "${host} NTP时间同步失败"
            fi
        else
            log "WARN" "跳过 ${host} NTP时间同步（SSH连接失败）"
        fi
    fi
}

# 设置主机时间（手动设置）
set_host_time() {
    local host=$1
    local target_time=$2

    log "INFO" "设置 ${host} 时间为: ${target_time}"

    if [ "$host" = "localhost" ]; then
        # 本地主机操作
        sudo date -s "${target_time}"
        check_result "设置本地主机时间失败"
        sudo hwclock -w
        check_result "写入本地硬件时钟失败"

        # 验证时间设置
        current_time=$(date)
        log "INFO" "本地主机当前时间: ${current_time}"
    else
        # 远程主机操作
        if check_ssh_connection ${host}; then
            ssh ${SSH_USER}@${host} "sudo date -s '${target_time}' && sudo hwclock -w" > /dev/null 2>&1
            check_result "${host} 主机时间同步失败，请检查SSH连接或权限"

            # 验证远程主机时间
            remote_time=$(ssh ${SSH_USER}@${host} "date" 2>/dev/null)
            log "INFO" "${host} 主机当前时间: ${remote_time}"
        else
            log "WARN" "跳过 ${host} 主机时间设置（SSH连接失败）"
        fi
    fi
}

# ======================== 5. 系统时间同步（103/104/106） ========================
log "INFO" "开始三主机时间同步操作..."

# 禁用所有主机的时间同步服务
disable_time_sync "localhost"
disable_time_sync "${HOST_104}"
disable_time_sync "${HOST_106}"

# 设置所有主机时间
set_host_time "localhost" "${TARGET_TIME}"
set_host_time "${HOST_104}" "${TARGET_TIME}"
set_host_time "${HOST_106}" "${TARGET_TIME}"

log "INFO" "103/104/106主机时间同步完成"

# ======================== 6. 关节自动标零 ========================
log "INFO" "开始关节自动标零..."
cd /opt/robot/bin/ || error_exit "进入标零程序目录失败"
sudo ./AutoSetHome -1 > joint_calib_log_$(date +'%Y%m%d%H%M').log 2>&1
check_result "关节自动标零失败，查看日志：/opt/robot/bin/joint_calib_log_$(date +'%Y%m%d%H%M').log"

# 验证标零结果（检查Pos值是否为0.000）
sudo minicom -C /tmp/joint_pos.log -c on -l -t xterm -e 'show_joint_pos; exit' > /dev/null 2>&1
NON_ZERO_POS=$(cat /tmp/joint_pos.log | grep 'Pos' | awk '{print $3}' | grep -v '0.000')
if [ -n "${NON_ZERO_POS}" ]; then
    error_exit "关节标零精度不达标，非零关节Pos值：${NON_ZERO_POS}"
fi

log "INFO" "关节自动标零完成，精度符合要求"

# ======================== 主程序入口 ========================
main() {
    log "INFO" "开始执行山猫M20 Pro三主机自动化脚本..."

    # 显示配置信息
    log "INFO" "配置信息："
    log "INFO" "  - WiFi名称：${WIFI_NAME}"
    log "INFO" "  - 目标时间：${TARGET_TIME}"
    log "INFO" "  - 序列号：${SERIAL_NO}"
    log "INFO" "  - 批次号：${BATCH_NO}"
    log "INFO" "  - 103主机：${HOST_103}"
    log "INFO" "  - 104主机：${HOST_104}"
    log "INFO" "  - 106主机：${HOST_106}"

    # 检查SSH连接状态
    log "INFO" "检查远程主机连接状态..."
    check_ssh_connection "${HOST_104}"
    check_ssh_connection "${HOST_106}"

    # 选择时间同步方式
    echo
    log "INFO" "请选择时间同步方式："
    log "INFO" "  1. 使用NTP服务器同步 (${NTP_SERVER})"
    log "INFO" "  2. 手动设置指定时间 (${TARGET_TIME})"

    read -p "请选择 (1/2，默认1): " sync_method
    sync_method=${sync_method:-1}

    echo
    if [ "$sync_method" = "1" ]; then
        log "INFO" "开始使用NTP服务器同步三主机时间..."

        # 使用NTP同步所有主机时间
        sync_time_with_ntp "${HOST_103}"
        sync_time_with_ntp "${HOST_104}"
        sync_time_with_ntp "${HOST_106}"

    else
        log "INFO" "开始手动设置三主机时间..."

        # 禁用所有主机的时间同步服务
        disable_time_sync "${HOST_103}"
        disable_time_sync "${HOST_104}"
        disable_time_sync "${HOST_106}"

        # 设置所有主机时间
        set_host_time "${HOST_103}" "${TARGET_TIME}"
        set_host_time "${HOST_104}" "${TARGET_TIME}"
        set_host_time "${HOST_106}" "${TARGET_TIME}"
    fi

    log "INFO" "103/104/106主机时间同步完成"

    # 显示最终状态
    log "INFO" "所有自动化操作执行完成！"
    log "INFO" "最终状态："
    log "INFO" "  - WiFi名称：${WIFI_NAME}"
    log "INFO" "  - 序列号：${SERIAL_NO}"
    log "INFO" "  - 批次号：${BATCH_NO}"
    log "INFO" "  - 系统时间：${TARGET_TIME}"
}

# ======================== 脚本使用说明 ========================
show_usage() {
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  -t TIME    设置目标时间 (格式: 'YYYY-MM-DD HH:MM:SS')"
    echo "  -n SERVER  设置NTP服务器 (默认: ${NTP_SERVER})"
    echo "  -h         显示此帮助信息"
    echo ""
    echo "时间同步方式:"
    echo "  1. NTP同步 - 使用NTP服务器自动同步时间（推荐）"
    echo "  2. 手动设置 - 设置指定的时间"
    echo ""
    echo "示例:"
    echo "  $0                              # 交互式选择同步方式"
    echo "  $0 -t '2025-08-20 11:11:15'     # 使用指定时间"
    echo "  $0 -n '192.168.1.100'           # 使用指定NTP服务器"
}

# ======================== 参数处理 ========================
while getopts "t:n:h" opt; do
    case $opt in
        t)
            TARGET_TIME="$OPTARG"
            log "INFO" "使用指定时间: ${TARGET_TIME}"
            ;;
        n)
            NTP_SERVER="$OPTARG"
            log "INFO" "使用指定NTP服务器: ${NTP_SERVER}"
            ;;
        h)
            show_usage
            exit 0
            ;;
        \?)
            echo "无效选项: -$OPTARG" >&2
            show_usage
            exit 1
            ;;
    esac
done

# ======================== 执行主程序 ========================
main



