@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 网络测试和设备检查脚本 (Windows版本)
:: 自动化执行网络连接、ping测试和minicom设备检查

echo =========================================
echo         网络测试和设备检查脚本
echo =========================================
echo.

:: 颜色设置 (Windows 10/11支持)
:: 使用PowerShell来实现彩色输出
set "ps_green=Write-Host '[INFO]' -ForegroundColor Green -NoNewline; Write-Host"
set "ps_yellow=Write-Host '[WARN]' -ForegroundColor Yellow -NoNewline; Write-Host"
set "ps_red=Write-Host '[ERROR]' -ForegroundColor Red -NoNewline; Write-Host"

:: 日志函数
:log_info
powershell -Command "%ps_green% ' %~1'"
goto :eof

:log_warn
powershell -Command "%ps_yellow% ' %~1'"
goto :eof

:log_error
powershell -Command "%ps_red% ' %~1'"
goto :eof

:: 网络连通性测试
:test_network
set "ip=%~1"
set "description=%~2"

echo.
call :log_info "正在测试 %description% (%ip%)..."

ping -n 3 "%ip%" >nul 2>&1
if %errorlevel% equ 0 (
    call :log_info "%description% 连接正常 ✓"
    set "result=0"
) else (
    call :log_error "%description% 连接失败 ✗"
    set "result=1"
)
goto :eof

:: SSH连接测试
:test_ssh_connection
set "ssh_target=user@************"

echo.
call :log_info "测试SSH连接到 %ssh_target%..."

:: 检查是否有SSH客户端
where ssh >nul 2>&1
if %errorlevel% neq 0 (
    call :log_warn "SSH客户端未找到，跳过SSH测试"
    goto :eof
)

:: 测试SSH连接
ssh -o ConnectTimeout=5 -o BatchMode=yes "%ssh_target%" exit >nul 2>&1
if %errorlevel% equ 0 (
    call :log_info "SSH连接测试成功 ✓"
) else (
    call :log_warn "SSH连接测试失败，可能需要密码认证"
)
goto :eof

:: minicom设备检查 (Windows版本使用PuTTY或其他串口工具)
:check_device_with_minicom
echo.
call :log_info "准备进行设备检查..."
call :log_warn "注意：以下操作需要在串口终端中手动执行"

echo.
echo 请在串口终端中依次执行以下命令：
echo 1. sh
echo 2. show all version
echo 3. show all error
echo 4. show battery
echo.

pause

:: 检查常用的串口工具
where putty >nul 2>&1
if %errorlevel% equ 0 (
    call :log_info "找到PuTTY，启动串口连接..."
    echo 请在PuTTY中配置串口连接参数
    start putty
    goto :minicom_end
)

where teraterm >nul 2>&1
if %errorlevel% equ 0 (
    call :log_info "找到TeraTerm，启动串口连接..."
    start teraterm
    goto :minicom_end
)

:: 如果没有找到串口工具，提供手动指导
call :log_warn "未找到常用串口工具 (PuTTY/TeraTerm)"
echo.
echo 请手动打开您的串口终端工具，然后执行上述命令
echo 常用串口工具：
echo - PuTTY: https://www.putty.org/
echo - TeraTerm: https://ttssh2.osdn.jp/
echo - Windows Terminal + WSL
echo.
pause

:minicom_end
echo.
call :log_info "串口会话结束"
goto :eof

:: 主函数
:main
echo.
call :log_info "开始执行网络测试和设备检查..."

:: 1. SSH连接测试
call :test_ssh_connection

:: 2. 网络连通性测试
call :test_network "10.21.33.201" "设备1"

:: 暂停10秒
call :log_info "等待10秒..."
timeout /t 10 /nobreak >nul

call :test_network "10.21.33.202" "设备2"

:: 暂停10秒
call :log_info "等待10秒..."
timeout /t 10 /nobreak >nul

:: 3. 设备检查
echo.
set /p "do_minicom=是否进行串口设备检查？(y/n): "
if /i "!do_minicom!"=="y" (
    call :check_device_with_minicom
) else (
    call :log_info "跳过串口设备检查"
)

echo.
echo =========================================
call :log_info "所有测试完成！"
echo =========================================

pause
goto :eof

:: 脚本入口
call :main
