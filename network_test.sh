#!/bin/bash

# 网络测试和设备检查脚本
# 自动化执行网络连接、ping测试和minicom设备检查

echo "========================================="
echo "        网络测试和设备检查脚本"
echo "========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 网络连通性测试
test_network() {
    local ip=$1
    local description=$2
    
    echo ""
    log_info "正在测试 $description ($ip)..."
    
    if ping -c 3 "$ip" > /dev/null 2>&1; then
        log_info "$description 连接正常 ✓"
        return 0
    else
        log_error "$description 连接失败 ✗"
        return 1
    fi
}

# SSH连接测试
test_ssh_connection() {
    local ssh_target="user@************"
    
    echo ""
    log_info "测试SSH连接到 $ssh_target..."
    
    # 测试SSH连接（超时10秒）
    if timeout 10 ssh -o ConnectTimeout=5 -o BatchMode=yes "$ssh_target" exit 2>/dev/null; then
        log_info "SSH连接测试成功 ✓"
        return 0
    else
        log_warn "SSH连接测试失败，可能需要密码认证"
        return 1
    fi
}

# minicom设备检查
check_device_with_minicom() {
    echo ""
    log_info "准备进行设备检查..."
    log_warn "注意：以下操作需要在minicom中手动执行"
    
    echo ""
    echo "请在minicom中依次执行以下命令："
    echo "1. sh"
    echo "2. show all version"
    echo "3. show all error"
    echo "4. show battery"
    echo ""
    
    read -p "按回车键启动minicom..."
    
    # 检查minicom是否安装
    if ! command -v minicom &> /dev/null; then
        log_error "minicom未安装，请先安装: sudo apt-get install minicom"
        return 1
    fi
    
    log_info "启动minicom..."
    sudo minicom
    
    echo ""
    log_info "minicom会话结束"
}

# 主函数
main() {
    echo ""
    log_info "开始执行网络测试和设备检查..."
    
    # 1. SSH连接测试
    test_ssh_connection
    
    # 2. 网络连通性测试
    test_network "10.21.33.201" "设备1"
    
    # 暂停10秒
    log_info "等待10秒..."
    sleep 10
    
    test_network "10.21.33.202" "设备2"
    
    # 暂停10秒
    log_info "等待10秒..."
    sleep 10
    
    # 3. 设备检查
    echo ""
    read -p "是否进行minicom设备检查？(y/n): " do_minicom
    if [[ "$do_minicom" =~ ^[Yy]$ ]]; then
        check_device_with_minicom
    else
        log_info "跳过minicom设备检查"
    fi
    
    echo ""
    echo "========================================="
    log_info "所有测试完成！"
    echo "========================================="
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
